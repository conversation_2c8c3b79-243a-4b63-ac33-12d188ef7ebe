import { useQuery } from "@tanstack/react-query";
import { Link } from "@tanstack/react-router";
import { createColumnHelper } from "@tanstack/react-table";
import { Edit, Eye, Trash } from "lucide-react";
import { useState } from "react";
import { useService } from "~/config/context/serviceProvider";
import { CategoryCode } from "~/modules/category/service/model/category";
import { productOptionsByCategoryCode } from "~/modules/product/hooks/product-options";
import type { ProductionFlow } from "../../service/model/production-flow";
import DeleteProductionFlowModal from "../DeleteProductionFlowModal";

const columnHelper = createColumnHelper<ProductionFlow>();

export const columns = [
	columnHelper.accessor("name", {
		header: "Nombre",
		cell: (info) => info.getValue(),
	}),
	columnHelper.accessor("code", {
		header: "Código",
		cell: (info) => info.getValue(),
	}),
	columnHelper.accessor("productID", {
		header: "Producto ID",
		cell: (info) => {
			const service = useService();
			const { data, isPending } = useQuery(
				productOptionsByCategoryCode(service, CategoryCode.PRODUCTS),
			);

			if (isPending) {
				return <span className="loading loading-spinner" />;
			}

			return (
				data?.find((product) => product.id === info.getValue())?.name || "-"
			);
		},
	}),
	columnHelper.display({
		id: "actions",
		header: "Acciones",
		cell: ({ row }) => {
			const [isDeleteOpen, setIsDeleteOpen] = useState(false);
			const productionFlow = row.original;

			return (
				<div className="flex gap-2">
					<Link
						to="/admin/manufacture/production-flow/edit/$id"
						params={{ id: productionFlow.id }}
						className="btn btn-sm btn-primary"
					>
						<Edit size={16} />
					</Link>
					<button
						type="button"
						className="btn btn-sm btn-error"
						onClick={() => setIsDeleteOpen(true)}
					>
						<Trash size={16} />
					</button>
					<DeleteProductionFlowModal
						isOpen={isDeleteOpen}
						setIsOpen={setIsDeleteOpen}
						productionFlow={productionFlow}
					/>
				</div>
			);
		},
	}),
];
