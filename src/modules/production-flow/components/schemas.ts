import * as v from "valibot";

// Schema for creating/editing production flow
export const ProductionFlowSchema = v.object({
	name: v.pipe(
		v.string("Debe ingresar un nombre"),
		v.minLength(1, "Debe tener al menos un caracter"),
	),
	code: v.pipe(
		v.string("Debe ingresar un código"),
		v.minLength(1, "Debe tener al menos un caracter"),
	),
	productID: v.pipe(
		v.string("Debe seleccionar un producto"),
		v.minLength(1, "Debe seleccionar un producto"),
	),
});
export type ProductionFlowSchema = v.InferOutput<typeof ProductionFlowSchema>;

// Schema for activity in the activities table
export const ActivitySchema = v.object({
	workAreaId: v.string("Debe seleccionar un área de trabajo"),
	operationId: v.string("Debe seleccionar una operación"),
	indexNumber: v.number("Número de índice requerido"),
	// Optional fields for display
	workAreaName: v.optional(v.string()),
	operationName: v.optional(v.string()),
	// Temporary ID for local state management
	tempId: v.optional(v.string()),
});
export type ActivitySchema = v.InferOutput<typeof ActivitySchema>;

// Schema for adding a new activity
export const AddActivitySchema = v.object({
	workAreaId: v.pipe(
		v.string("Debe seleccionar un área de trabajo"),
		v.minLength(1, "Debe seleccionar un área de trabajo"),
	),
	operationId: v.pipe(
		v.string("Debe seleccionar una operación"),
		v.minLength(1, "Debe seleccionar una operación"),
	),
});
export type AddActivitySchema = v.InferOutput<typeof AddActivitySchema>;

// Schema for the complete production flow with activities form
export const ProductionFlowWithActivitiesSchema = v.object({
	name: v.pipe(
		v.string("Debe ingresar un nombre"),
		v.minLength(1, "Debe tener al menos un caracter"),
	),
	code: v.pipe(
		v.string("Debe ingresar un código"),
		v.minLength(1, "Debe tener al menos un caracter"),
	),
	productID: v.pipe(
		v.string("Debe seleccionar un producto"),
		v.minLength(1, "Debe seleccionar un producto"),
	),
	activities: v.pipe(
		v.array(ActivitySchema),
		v.minLength(1, "Debe agregar al menos una actividad"),
	),
});
export type ProductionFlowWithActivitiesSchema = v.InferOutput<typeof ProductionFlowWithActivitiesSchema>;
