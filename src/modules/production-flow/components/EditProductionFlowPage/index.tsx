import { Hash, Plus, Tag } from "lucide-react";
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";
import { productOptions } from "src/modules/product/hooks/product-options";
import type { ProductionFlowWithActivities } from "../../service/model/production-flow";
import ActivitiesTable from "../ActivitiesTable";
import AddActivityModal from "../AddActivityModal";
import type { ActivitySchema } from "../schemas";
import useEditProductionFlowPage from "./use-edit-production-flow-page";

interface EditProductionFlowFormProps {
	id: string;
	data: ProductionFlowWithActivities;
	activities: ActivitySchema[];
	setActivities: React.Dispatch<React.SetStateAction<ActivitySchema[]>>;
}

export default function EditProductionFlowForm({
	id,
	data,
	activities,
	setActivities,
}: EditProductionFlowFormProps) {
	const service = useService();
	const { productionFlow } = service;
	const [isAddActivityOpen, setIsAddActivityOpen] = useState(false);

	const { form, handleSubmit, isPending, isInitialized } =
		useEditProductionFlowPage({
			id,
			productionFlowData: data,
			activities,
			setActivities,
		});

	// Get products for selection
	const { data: products = [] } = useQuery(productOptions(service));

	const handleAddActivity = (activity: ActivitySchema) => {
		setActivities((prev) => [...prev, activity]);
	};

	const handleReorderActivities = (reorderedActivities: ActivitySchema[]) => {
		setActivities(reorderedActivities);
	};

	const handleDeleteActivity = (tempId: string) => {
		setActivities((prev) =>
			prev.filter((activity) => activity.tempId !== tempId),
		);
	};

	const nextIndexNumber = activities.length + 1;

	if (!isInitialized) {
		return (
			<div className="flex h-64 items-center justify-center">
				<span className="loading loading-spinner loading-lg" />
			</div>
		);
	}

	return (
		<>
			<div className="space-y-6">
				{/* Production Flow Form */}
				<div className="card bg-base-100 shadow-sm">
					<div className="card-body">
						<h2 className="card-title">Información del Flujo</h2>
						<form.AppForm>
							<fieldset className="fieldset">
								<form.AppField
									name="name"
									children={({ FSTextField }) => (
										<FSTextField
											label="Nombre"
											placeholder="Nombre del flujo de producción"
											prefixComponent={<Tag size={16} />}
										/>
									)}
								/>
								<form.AppField
									name="code"
									validators={{
										onChangeAsyncDebounceMs: 500,
										onChangeAsync: async ({ value }) => {
											if (
												!value ||
												value.trim() === "" ||
												value === data.productionFlow.code
											) {
												return undefined;
											}
											try {
												await AppRuntime.runPromise(
													productionFlow.validateCode(value),
												);
												return undefined;
											} catch (e) {
												return [{ message: "El código ya existe" }];
											}
										},
									}}
									children={({ FSTextField }) => (
										<FSTextField
											label="Código"
											placeholder="Código del flujo de producción"
											prefixComponent={<Hash size={16} />}
										/>
									)}
								/>
								<form.AppField
									name="productID"
									children={({ FSComboBoxField }) => (
										<FSComboBoxField
											label="Producto"
											placeholder="Seleccionar producto"
											options={products.map((product) => ({
												value: product.id,
												label: `${product.name} (${product.code})`,
											}))}
										/>
									)}
								/>
							</fieldset>
						</form.AppForm>
					</div>
				</div>

				{/* Activities Section */}
				<div className="space-y-4">
					<div className="flex items-center justify-between">
						<h2 className="font-semibold text-xl">Actividades</h2>
						<button
							type="button"
							className="btn btn-primary btn-sm"
							onClick={() => setIsAddActivityOpen(true)}
						>
							<Plus size={16} />
							Agregar Actividad
						</button>
					</div>

					<ActivitiesTable
						activities={activities}
						onReorder={handleReorderActivities}
						onDelete={handleDeleteActivity}
					/>
				</div>

				{/* Submit Button */}
				<div className="card bg-base-100 shadow-sm">
					<div className="card-body">
						<div className="flex justify-end gap-4">
							<a
								href="/admin/manufacture/production-flow"
								className="btn btn-ghost"
							>
								Cancelar
							</a>
							<button
								type="button"
								className="btn btn-primary"
								onClick={handleSubmit}
								disabled={isPending || activities.length === 0}
							>
								{isPending ? (
									<span className="loading loading-spinner loading-sm" />
								) : (
									"Actualizar Flujo de Producción"
								)}
							</button>
						</div>
						{activities.length === 0 && (
							<p className="mt-2 text-error text-sm">
								Debe tener al menos una actividad en el flujo de producción.
							</p>
						)}
					</div>
				</div>
			</div>

			<AddActivityModal
				isOpen={isAddActivityOpen}
				setIsOpen={setIsAddActivityOpen}
				onAddActivity={handleAddActivity}
				nextIndexNumber={nextIndexNumber}
			/>
		</>
	);
}
